<?php

namespace V4;

use Google\Client as Google_Client;
use DateTimeZone;
use DateTime;

/**
 * Instructor model
 *
 */
class InstructorModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadModel('user');
        $this->loadModel('org');
        $this->loadModel('class');
        $this->loadModel('google');
        $this->loadModel('course');
    }

    /**
     * Retrieves the details of the instructor.
     *
     *
     * @return WP_REST_Response The response object containing the user's details.
     * @throws WP_Error If an exception occurs.
     */


    public function getInstructor($query, $filter = [])
    {

        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $qryStr = [];
            // Schema-aware field selection similar to UserModel
            if (function_exists('ynSchemaType') && ynSchemaType($filter, 'Instructor_Basic')) {
                $qryStr['_source'] = implode(',', [
                    'data.details.user_id',
                    'data.details.native_language',
                    'data.details.learner_avg_class_rating',
                    'data.details.reviews_count',
                    'data.details.mapped_courses',
                    'data.details.account_login_status',
                    'data.details.working_hours'
                ]);
            } else {
                $qryStr['_source'] = implode(',', [
                    'data.details.user_id',
                    'data.details.instructor_experience',
                    'data.details.native_language',
                    'data.details.fluent_in',
                    'data.details.learner_avg_class_rating',
                    'data.details.staff_avg_class_rating',
                    'data.details.reviews_count',
                    'data.details.mapped_courses',
                    'data.details.mapped_categories',
                    'data.details.account_login_status',
                    'data.details.working_hours'
                ]);
            }

            if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                $qryStr['_source'] = implode(',', $query['_source']);
            }
            $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id'], $qryStr);
        } else {
            return false;
        }

        $instructor = $instructorDataResponse['body']['_source']['data']['details'];
        $userDetails = $this->userModel->getUser($instructor['user_id'], ['schema' => 'User_Minimal']);

        // --- Start Conditional Schema Response ---
        if (function_exists('ynSchemaType') && ynSchemaType($filter, 'Instructor_Basic')) {
            $courseData = [];
            if (!empty($instructor['mapped_courses'])) {
                foreach ($instructor['mapped_courses'] as $courseId) {
                    $course = $this->courseModel->getCourse(['id' => $courseId], ['schema' => 'Course_Minimal']);
                    if ($course && is_array($course) && isset($course['id'])) {
                        $courseData[] = $course;
                    }
                }
            }

            $responseData = [
                'user' => $userDetails,
                'native_language' => [
                    "name_in_english" => $instructor['native_language'] ?? '',
                    "native_lang_name" => $instructor['native_language'] ?? '',
                    "code" => substr(strtolower($instructor['native_language'] ?? 'en'), 0, 2)
                ],
                'has_mapped_courses' => !empty($courseData),
                'avg_rating' => (float)($instructor['learner_avg_class_rating'] ?? 0),
                'review_count' => (int)($instructor['reviews_count'] ?? 0),
                'is_enabled' => ($instructor['account_login_status'] ?? 'de-active') === 'active',
                'has_working_hours' => !empty($instructor['working_hours']),
            ];
            // For minimal schema, we validate against 'Instructor_Basic' directly
            return $this->schema->validate($responseData, 'Instructor_Basic', $filter);
        }
        // --- End Conditional Schema Response ---


        // --- Full Instructor Response (existing logic) ---
        $fluentLanguagesData = [];
        foreach (explode(',', $instructor['fluent_in'] ?? '') as $language) {
            if(empty($language)) continue;
            $fluentLanguagesData[] = [
                "name_in_english" => $language,
                "native_lang_name" => '',
                "code" => ''
            ];
        }

        $courseData = [];
        if (!empty($instructor['mapped_courses'])) {
            foreach ($instructor['mapped_courses'] as $courseId) {
                $courseData[] = $this->load->subData('course', 'getCourse', ['id' => $courseId], ['schema' => 'Course_Minimal']);
            }
        }

        $categoryData = [];
        if (!empty($instructor['mapped_categories'])) {
            foreach (array_unique($instructor['mapped_categories']) as $categoryId) {
                $categoryData[] = $this->load->subData('category', 'getCategory', ['id' => $categoryId], ['schema' => 'Category_Minimal']);
            }
        }

        $instructorNameForSchedule = $userDetails['data']['details']['name'] ?? 'Instructor';

        $workingHours = $instructor['working_hours'] ?? [];
        $formattedWorkingHours = [];

        if (is_array($workingHours) && !empty($workingHours)) {
            $formattedWorkingHours = array_map(function ($dayData) {
                if (!is_array($dayData) || empty($dayData['day'])) {
                    return [
                        'day' => '',
                        'name' => '',
                        'is_available' => false,
                        'working_hours' => 0,
                        'time_slot' => [['start' => ['time' => '', 'timezone' => ''], 'end' => ['time' => '', 'timezone' => '']]]
                    ];
                }

                $daySlots = $dayData['slots'] ?? [];
                $dayShort = strtoupper(substr($dayData['day'], 0, 3));
                $totalHours = 0;
                $mergedSlots = [];

                $currentStart = null;
                $lastEnd = null;

                if (is_array($daySlots)) {
                    foreach ($daySlots as $slot) {
                        if (is_array($slot) && !empty($slot['status']) && isset($slot['starttime']) && isset($slot['endtime'])) {
                            if ($currentStart === null) {
                                $currentStart = $slot['starttime'];
                            }
                            $lastEnd = $slot['endtime'];
                            if (strtotime($slot['endtime']) !== false && strtotime($slot['starttime']) !== false) {
                                $totalHours += (strtotime($slot['endtime']) - strtotime($slot['starttime'])) / 3600;
                            }
                        } else {
                            if ($currentStart !== null && $lastEnd !== null) {
                                $mergedSlots[] = [
                                    'start' => [
                                        'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                                        'timezone' => $this->locale->activeTimezone()
                                    ],
                                    'end' => [
                                        'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                                        'timezone' => $this->locale->activeTimezone()
                                    ]
                                ];
                                $currentStart = null;
                                $lastEnd = null;
                            }
                        }
                    }
                }

                if ($currentStart !== null && $lastEnd !== null) {
                    $mergedSlots[] = [
                        'start' => [
                            'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                            'timezone' => $this->locale->activeTimezone()
                        ]
                    ];
                }

                return [
                    'day' => $dayShort,
                    'name' => $this->dt->getWeekDays($dayShort),
                    'is_available' => !empty($mergedSlots),
                    'working_hours' => round($totalHours, 1),
                    'time_slot' => !empty($mergedSlots) ? $mergedSlots : [[
                        'start' => ['time' => '', 'timezone' => ''],
                        'end' => ['time' => '', 'timezone' => '']
                    ]]
                ];
            }, $workingHours);
        }

        // Generate profile URL following the same pattern as other controllers
        $profileUserIdReference = get_user_meta($instructor['user_id'], 'profile_user_id_reference', true);
        $profileUrl = '';
        if (!empty($profileUserIdReference)) {
            $profileUrl = get_permalink($profileUserIdReference);
        }
        // Fallback to name-based URL if no profile reference found or permalink failed
        if (empty($profileUrl) || $profileUrl === false) {
            $profileUrl = site_url("/profile/" . strtolower($userDetails['full_name'] ?? 'instructor'));
        }

        $responseData = [
            'user' => $userDetails,
            'about' => $instructor['instructor_experience'] ?? '',
            'profile_url' => $profileUrl,
            'native_language' => [
                "name_in_english" => $instructor['native_language'] ?? '',
                "native_lang_name" => $instructor['native_language'] ?? '',
                "code" => substr(strtolower($instructor['native_language'] ?? 'en'), 0, 2)
            ],
            'fluent_in_languages' => $fluentLanguagesData,
            'has_mapped_courses' => !empty($courseData),
            'mapped_courses' => !empty($courseData) ? $courseData : [],
            'avg_rating' => (float) ($instructor['learner_avg_class_rating'] ?? 0),
            'review_count' => (int) ($instructor['reviews_count'] ?? 0),
            'active_learners' => 0,
            'past_learners' => 0,
            'is_enabled' => ($instructor['account_login_status'] ?? 'de-active') === 'active',
            'current_state' => [
                'has_phone_number' => !empty($userDetails['phone'] ?? ''),
                'active_category' => [
                    'id' => 0,
                    'name' => '',
                    'slug' => ''
                ],
                'org_id' => 0,
                'redirect_url' => ''
            ],
            'has_working_hours' => !empty($formattedWorkingHours)
        ];

        return $this->schema->validate($responseData, 'Instructor', $filter);
    }

    /**
     * Checks instructor's calendar availability for a given time slot.
     *
     * Verifies instructor's availability by checking Google Calendar for conflicts,
     * processes available time slots, and returns formatted availability data.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Contains resource_id, start_date, end_date, start_time, end_time, org_id, class_id
     * @param array $filter Optional filter for availability check
     * @return array|bool Returns formatted availability data or false on failure
     */
    public function getInstructorAvailability($query, $filter)
    {
        if (empty($query['resource_id']) || empty($query['start_date']) || empty($query['end_date']) || empty($query['start_time']) || empty($query['end_time'])) {
            return false;
        }

        $this->loadModel('class');

        $userId = $query['resource_id'];
        $startDate = $query['start_date'];
        $endDate = $query['end_date'];
        $startTime = $query['start_time'];
        $endTime = $query['end_time'];
        $orgId = $query['org_id'] ?? null;
        $classId = $query['class_id'] ?? null;

        $accessToken = $this->classModel->getGoogleMeetAccessToken($userId, $this->locale->activeTimezone(), $orgId);
        if (empty($accessToken)) {
            return false;
        }

        $client = new Google_Client();
        $client->setAccessToken($accessToken);

        $calendarId = 'primary';
        if (empty($calendarId)) {
            return false;
        }

        $instructorName = get_user_meta($userId, 'yuno_display_name', true);
        $instructors = [
            [
                'id' => $userId,
                'name' => $instructorName
            ]
        ];
        $instructorCalendarIds = [$calendarId];

        $availableSlotsRaw = $this->getAvailableSlotsResource($client, $instructorCalendarIds, $startDate, $endDate, $startTime, $endTime, $instructors);

        $finalResponse = []; 

        if (!empty($availableSlotsRaw['available_slots'])) {
            foreach ($availableSlotsRaw['available_slots'] as $day) {
                $date = $day['date'];

                $isAvailableForDay = true;
                foreach ($day['slots'] as $slot) {
                    if (empty($slot['status'])) {
                        $isAvailableForDay = false;
                        break;
                    }
                }

                $finalResponse[] = [
                    'resource' => [
                        'type' => 'Instructor',
                        'name' => $instructors[0]['name']
                    ],
                    'time_slots' => [
                        'time_slot' => [
                            'start' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $startTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ],
                            'end' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $endTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ]
                        ],
                        'is_available' => $isAvailableForDay
                    ]
                ];
            }
        }

        $validatedResponse = [];
        foreach ($finalResponse as $item) {
            try {
                $validatedItem = $this->schema->validate($item, 'Availability', $filter);
                $validatedResponse[] = $validatedItem;
            } catch (\Exception $e) { // Using \Exception for global namespace
                return false;
            }
        }

        return $validatedResponse;
    }


    public function getAvailableSlotsResource($client, $calendarId, $startDate, $endDate, $startTime, $endTime, $instructors)
    {
        $date = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $end->modify('+1 day');
        $timeZone = $this->locale->activeTimezone();

        $this->loadModel('google');

        $calendarTimeZone = $this->googleModel->getCalendarTimeZone($client, $calendarId);

        $availableSlots = [];

        while ($date < $end) {
            $currentDate = $date->format('Y-m-d');
            $dayOfWeek = $date->format('l');
            $timeMin = "$currentDate $startTime";
            $timeMax = "$currentDate $endTime";

            $timeMinDT = new \DateTime($timeMin, new \DateTimeZone($calendarTimeZone));
            $timeMaxDT = new \DateTime($timeMax, new \DateTimeZone($calendarTimeZone));

            $combinedBusyTimes = $this->getCombinedBusyTimes($client, $calendarId, $timeMin, $timeMax);
            $slots = $this->generateTimeSlots($timeMinDT, $timeMaxDT, $combinedBusyTimes);

            $availableSlots[] = [
                'date' => $currentDate,
                'day_of_week' => $dayOfWeek,
                'slots' => $slots
            ];

            $output = [
                'instructors' => $instructors,
                'available_slots' => $availableSlots
            ];

            $date->modify('+1 day');
        }

        return $output;
    }

    public function getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax)
    {
        $combinedBusyTimes = [];

        foreach ($calendarIds as $calendarId) {
            $busyTimes = $this->googleModel->getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax);
            foreach ($busyTimes as $busyTime) {
                $busyStart = new \DateTime($busyTime['start']);
                $busyEnd = new \DateTime($busyTime['end']);
                $combinedBusyTimes[] = [
                    'start' => $busyStart,
                    'end' => $busyEnd
                ];
            }
        }

        usort($combinedBusyTimes, function ($a, $b) {
            return $a['start'] <=> $b['start'];
        });

        return $combinedBusyTimes;
    }

    public function generateTimeSlots($timeMin, $timeMax, $combinedBusyTimes)
    {
        $slots = [];
        $currentSlotStart = clone $timeMin;

        while ($currentSlotStart < $timeMax) {
            $currentSlotEnd = clone $currentSlotStart;
            $currentSlotEnd->modify('+30 minutes');
            if ($currentSlotEnd > $timeMax) {
                $currentSlotEnd = clone $timeMax;
            }

            $isFree = true;
            foreach ($combinedBusyTimes as $busyTime) {
                $busyStart = $busyTime['start'];
                $busyEnd = $busyTime['end'];

                if (($busyStart < $currentSlotEnd && $busyEnd > $currentSlotStart)) {
                    $isFree = false;
                    break;
                }
            }

            $slots[] = [
                'starttime' => $currentSlotStart->format('H:i'),
                'endtime' => $currentSlotEnd->format('H:i'),
                'status' => $isFree
            ];

            $currentSlotStart = clone $currentSlotEnd;
        }

        return $slots;
    }

    public function getInstructorVirtualClasserooms($query, $filter = [])
    {
        if (isset($query['custom'])) {
            $instructorOrgDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }

        if ($instructorOrgDataResponse['status_code'] == 200) {
            $organizations = $instructorOrgDataResponse['body']['aggregations']['distinct_org_ids']['org_ids']["buckets"];
            // Build the structured response
            if (count($organizations)) {

                foreach ($organizations as $organization) {
                    $details = $organization['sample_field']['hits']['hits'][0]['_source'];

                    $vcQuery['custom'] = [
                        'id' => $query['params']['instructorId'],
                        'orgId' => $details['org_id'],
                        'platform' => $this->loadModel('org')->getOrganization(
                            [
                                'id' => $details['org_id'],
                                '_source' => [
                                    'data.details.org_vc_app'
                                ]
                            ],
                            [
                                'schema' => [
                                    'virtual_classroom' =>
                                    [
                                        'platform' => 'string'
                                    ]
                                ],
                                'key' => 'virtual_classroom->platform'
                            ]
                        )
                    ];

                    $responseData[] = [
                        'virtual_classroom' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $vcQuery, ['noResponse' => 'Virtual_Classroom']),
                        'org' => $this->load->subData("org", "getOrganization", $details['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => true]),
                        'academies' => $this->load->subData("academy", "getAcademies", ['orgId' => $details['org_id']], ['schema' => 'Refer#Academy_Minimal', 'key' => 'data', 'noResponse' => true]),
                    ];
                }

                $dataSchema = [[
                    "virtual_classroom" => 'Refer#Virtual_Classroom',
                    "org" => 'Refer#Organization_Minimal',
                    "academies" => ['Refer#Academy_Minimal'],
                ]];

                return $this->schema->validate($responseData, $dataSchema, $filter);
            }
        }
        return false;
    }

    /**
     * Generates instructor filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The selected instructor ID (if any).
     * @param int $learnerId The ID of the learner (for filtering learner-specific instructors).
     * @param int $counselorId The ID of the counselor (for filtering counselor-specific instructors).
     * @return array Returns an array containing instructor filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'instructor_id',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $instructorId, //  Pre-select instructor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];

    }
    public function generateEnrollmentInstructorFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'instructor',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'dropdown',
            'selected' => $instructorId, //  Pre-select instructor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "instructor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select instructor if provided
        if ($instructorId > 0) {
            $instructorData = $this->getInstructor($instructorId);
            if (!empty($instructorData)) {
                $filterData['selected'] = $instructorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all instructors (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'learner' && $learnerId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.learner_id" => $learnerId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'counselor' && $counselorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.counselor_id" => $counselorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Instructor Query: " . json_encode($customQuery));

        //  Fetch Instructors from Elasticsearch
        $instructorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($instructorRecords['status_code']) && $instructorRecords['status_code'] === 200) {
            $instructors = $instructorRecords['body']['hits']['hits'];

            foreach ($instructors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundInstructorId = $details['user_id'] ?? 0;
                $instructorName = $userDetails['name'] ?? '';
                $instructorEmail = $userDetails['email'] ?? '';

                if ($foundInstructorId) {
                    $filterData['items'][] = [
                        'id' => $foundInstructorId,
                        'label' => $instructorName . " (" . $instructorEmail . ")",
                        'filter' => 'instructor'
                    ];
                }
            }
        }

        return $filterData;
    }

    /**
     * Returns a default 7-day working hours structure with all days marked unavailable.
     * Ignores any actual instructor data or working hour availability.
     *
     * @param mixed $query (Optional, to match method signature if needed)
     * @return array
     */
    public function getBlankWorkingHours($query = null)
    {
        $defaultDays = [
            ['day' => 'MON', 'name' => 'Monday'],
            ['day' => 'TUE', 'name' => 'Tuesday'],
            ['day' => 'WED', 'name' => 'Wednesday'],
            ['day' => 'THU', 'name' => 'Thursday'],
            ['day' => 'FRI', 'name' => 'Friday'],
            ['day' => 'SAT', 'name' => 'Saturday'],
            ['day' => 'SUN', 'name' => 'Sunday']
        ];

        $days = array_map(function ($day) {
            return [
                'day' => $day['day'],
                'name' => $day['name'],
                'is_available' => false,
                'working_hours' => 0,
                'time_slot' => [[
                    'start' => ['time' => '', 'timezone' => ''],
                    'end' => ['time' => '', 'timezone' => '']
                ]]
            ];
        }, $defaultDays);

        return [
            'resource' => [
                'type' => 'INSTRUCTOR',
                'name' => 'Instructor'
            ],
            'days' => $days
        ];
    }

    public function getClassDays(string $startDate, string $endDate): array
    {
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $end->modify('+1 day');

        $interval = new \DateInterval('P1D');
        $dateRange = new \DatePeriod($start, $interval, $end);

        $days = [];
        foreach ($dateRange as $date) {
            $day = $date->format('l'); 
            if (!in_array($day, $days)) {
                $days[] = $day;
            }
        }

        return $days;
    }

    public function checkInstructorsWorkingOnDays(array $resourceIds, array $days)
    {
        $resourceIds = array_map(fn($id) => "instructorsignedup-{$id}", $resourceIds);

        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['terms' => ['_id' => $resourceIds]],
                        [
                            'nested' => [
                                'path' => 'data.details.working_hours',
                                'query' => [
                                    'bool' => [
                                        'must' => [
                                            ['terms' => ['data.details.working_hours.day.keyword' => $days]],
                                            [
                                                'nested' => [
                                                    'path' => 'data.details.working_hours.slots',
                                                    'query' => [
                                                        'term' => [
                                                            'data.details.working_hours.slots.status' => true
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => ['_id']
        ];

        $response = $this->es->customQuery($query, 'instructorsignedup');

        if ($response['status_code'] === 200 && !empty($response['body']['hits']['hits'])) {
            $workingResourceIds = array_map(function ($hit) {
                return str_replace('instructorsignedup-', '', $hit['_id']);
            }, $response['body']['hits']['hits']);

            return [
                'status' => 'success',
                'message' => 'Found working instructors',
                'data' => [
                    'is_working' => true,
                    'resource_ids' => $workingResourceIds
                ]
            ];
        }

        return [
            'status' => 'fail',
            'message' => 'No instructors working on given days',
            'data' => [
                'is_working' => false,
                'resource_ids' => []
            ]
        ];
    }

    public function getWorkingHoursForInstructorDay($instructorId, $dayOfWeek)
    {
        $esId = "instructorsignedup-" . $instructorId;
        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['term' => ['_id' => $esId]],
                        [
                            'nested' => [
                                'path' => 'data.details.working_hours',
                                'query' => [
                                    'bool' => [
                                        'must' => [
                                            ['term' => ['data.details.working_hours.day.keyword' => $dayOfWeek]]
                                        ]
                                    ]
                                ],
                                'inner_hits' => ['name' => 'working_day']
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => ['data.details.working_hours']
        ];
        $response = $this->es->customQuery($query, 'instructorsignedup');

        if (empty($response['body']['hits']['hits'])) return [];

        $hit = $response['body']['hits']['hits'][0];
        $innerHits = $hit['inner_hits']['working_day']['hits']['hits'] ?? [];
        if (empty($innerHits)) return [];

        $slots = $innerHits[0]['_source']['slots'] ?? [];
        $activeSlots = array_filter($slots, fn($s) => $s['status'] === true);

        usort($activeSlots, fn($a, $b) => strcmp($a['starttime'], $b['starttime']));
        $ranges = [];
        $current = null;

        foreach ($activeSlots as $slot) {
            if (!$current) {
                $current = ['start' => $slot['starttime'], 'end' => $slot['endtime']];
                continue;
            }
            if ($slot['starttime'] === $current['end']) {
                $current['end'] = $slot['endtime'];
            } else {
                $ranges[] = $current;
                $current = ['start' => $slot['starttime'], 'end' => $slot['endtime']];
            }
        }

        if ($current) $ranges[] = $current;
        return $ranges;
    }

    public function getAvailabilityOfInstructor($resourceIds, $startDate, $endDate, $startTime, $endTime)
    {
        $this->loadModel('google');
        $resourceIds = is_array($resourceIds) ? $resourceIds : [$resourceIds];
        $availability = [];
        $timeSlotsMap = [];
        $timezone = $this->locale->activeTimezone() ?? 'UTC';

        foreach ($resourceIds as $instructorId) {
            $calendarId = get_user_meta($instructorId, 'user_google_calendar_id', true);
            $instructorName = get_user_meta($instructorId, 'yuno_display_name', true);

            if (empty($calendarId)) {
                $availability[] = [
                    'id' => $instructorId,
                    'name' => $instructorName,
                    'status' => false,
                    'message' => 'No calendar ID found for the user'
                ];
                continue;
            }

            $vcData = get_user_meta($instructorId, 'virtual_classroom_data', true);
            $currentOrg = $vcData['data'][0]['org_id'] ?? null;
            $accessTokenResponse = get_google_meet_access_token($instructorId, $currentOrg);

            if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse)) {
                $availability[] = [
                    'id' => $instructorId,
                    'name' => $instructorName,
                    'status' => false,
                    'message' => 'Unable to retrieve access token'
                ];
                continue;
            }

            $client = new \Google_Client();
            $client->setAccessToken($accessTokenResponse);

            $dateCursor = new \DateTime($startDate);
            $endCursor = new \DateTime($endDate);
            $endCursor->modify('+1 day');

            while ($dateCursor < $endCursor) {
                $currentDate = $dateCursor->format('Y-m-d');
                $dayOfWeek = $dateCursor->format('l');

                $workingHours = $this->getWorkingHoursForInstructorDay($instructorId, $dayOfWeek);

                if (empty($workingHours)) {
                    $dateCursor->modify('+1 day');
                    continue;
                }

                foreach ($workingHours as $range) {
                    $slotsData = $this->googleModel->getAvailableSlotsResource(
                        $client,
                        [$calendarId],
                        $currentDate,
                        $currentDate,
                        $range['start'],
                        $range['end'],
                        [[
                            'id' => $instructorId,
                            'name' => $instructorName,
                            'status' => true,
                            'message' => 'Available calendar data retrieved successfully'
                        ]]
                    );

                    if (!empty($slotsData['time_slots'])) {
                        foreach ($slotsData['time_slots'] as $slotDay) {
                            $dateKey = $slotDay['date'];

                            if (!isset($timeSlotsMap[$dateKey])) {
                                $timeSlotsMap[$dateKey] = [
                                    'date' => $dateKey,
                                    'day_of_week' => $slotDay['day_of_week'] ?? '',
                                    '_slotMap' => []
                                ];
                            }

                            foreach ($slotDay['slots'] as $slot) {
                                if (!$slot['status']) continue;

                                $key = "{$slot['starttime']}-{$slot['endtime']}";

                                if (!isset($timeSlotsMap[$dateKey]['_slotMap'][$key])) {
                                    $timeSlotsMap[$dateKey]['_slotMap'][$key] = [
                                        'start' => [
                                            'time' => $slot['starttime'],
                                            'timezone' => $timezone
                                        ],
                                        'end' => [
                                            'time' => $slot['endtime'],
                                            'timezone' => $timezone
                                        ],
                                        'status' => true,
                                        'resource_ids' => [$instructorId]
                                    ];
                                } else {
                                    $timeSlotsMap[$dateKey]['_slotMap'][$key]['resource_ids'][] = $instructorId;
                                }
                            }
                        }
                    }
                }

                $dateCursor->modify('+1 day');
            }

            $availability[] = [
                'id' => $instructorId,
                'name' => $instructorName,
                'status' => true,
                'message' => 'Processed availability successfully'
            ];
        }

        foreach ($timeSlotsMap as &$entry) {
            $entry['slots'] = array_values($entry['_slotMap']);
            unset($entry['_slotMap']);
        }

        return [
            'instructors' => $availability,
            'time_slots' => array_values($timeSlotsMap)
        ];
    }

    public function getInstructorWorkingHours($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.user_id',
                'data.details.working_hours'
            ]);

            if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                $qryStr['_source'] = implode(',', $query['_source']);
            }

            $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id'], $qryStr);
        } else {
            return false;
        }

        if ($instructorDataResponse['status_code'] == 200) {
            $instructor = $instructorDataResponse['body']['_source']['data']['details'];

            $responseData = [
                
                    'resource' => [
                        'type' => 'INSTRUCTOR',
                        'name' => 'Instructor'
                    ],
                    'days' => !empty($instructor['working_hours']) ? (function ($workingHours) {
                        $allDays = [];

                        foreach ($workingHours as $dayData) {
                            $daySlots = $dayData['slots'] ?? [];
                            $dayShort = strtoupper(substr($dayData['day'], 0, 3));
                            $totalHours = 0;
                            $mergedSlots = [];

                            $currentStart = null;
                            $lastEnd = null;

                            foreach ($daySlots as $slot) {
                                if (!empty($slot['status'])) {
                                    if ($currentStart === null) {
                                        $currentStart = $slot['starttime'];
                                    }
                                    $lastEnd = $slot['endtime'];
                                    $totalHours += (strtotime($slot['endtime']) - strtotime($slot['starttime'])) / 3600;
                                } else {
                                    if ($currentStart !== null && $lastEnd !== null) {
                                        $mergedSlots[] = [
                                            'start' => [
                                                'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                                                'timezone' => $this->locale->activeTimezone()
                                            ],
                                            'end' => [
                                                'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                                                'timezone' => $this->locale->activeTimezone()
                                            ]
                                        ];
                                        $currentStart = null;
                                        $lastEnd = null;
                                    }
                                }
                            }

                            // Finalize the last open slot range
                            if ($currentStart !== null && $lastEnd !== null) {
                                $mergedSlots[] = [
                                    'start' => [
                                        'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                                        'timezone' => $this->locale->activeTimezone()
                                    ],
                                    'end' => [
                                        'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                                        'timezone' => $this->locale->activeTimezone()
                                    ]
                                ];
                            }

                            $allDays[] = [
                                'day' => $dayShort,
                                'name' => $this->dt->getWeekDays($dayShort),
                                'is_available' => !empty($mergedSlots),
                                'working_hours' => round($totalHours, 1),
                                'time_slot' => !empty($mergedSlots) ? $mergedSlots : [[
                                    'start' => ['time' => '', 'timezone' => ''],
                                    'end' => ['time' => '', 'timezone' => '']
                                ]]
                            ];
                        }

                        return $allDays;
                    })($instructor['working_hours']) : false
                ];
            

            // print_r($responseData);exit;
            return $this->schema->validate($responseData, 'Working_Hours', $filter);
        }

        return false;
    }



    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $instructorData Data for instructor creation
     * @return array|WP_Error Result of instructor creation
     */
    public function createInstructor($instructorData)
    {
        $codes = error_code_setting();
        
        // Check if we're creating a new user or using existing one
        if (empty($instructorData['user_id']) && !empty($instructorData['email'])) {
            // Create new user first
            $random_password = wp_generate_password(12, false);
            $email = sanitize_email($instructorData['email']);
            $username = sanitize_user($email);
            
            // Check if username exists
            if (username_exists($username)) {
                return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'Email already in use', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
            }
            
            // Create user
            $user_id = wp_create_user($username, $random_password, $email);
            
            if (is_wp_error($user_id)) {
                return $user_id;
            }
            
            // Set user meta
            wp_update_user([
                'ID' => $user_id,
                'first_name' => sanitize_text_field($instructorData['first_name']),
                'last_name' => sanitize_text_field($instructorData['last_name']),
                'display_name' => sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name'])
            ]);
            
            update_user_meta($user_id, 'yuno_gplus_email', $email);
            update_user_meta($user_id, 'yuno_first_name', sanitize_text_field($instructorData['first_name']));
            update_user_meta($user_id, 'yuno_last_name', sanitize_text_field($instructorData['last_name']));
            update_user_meta($user_id, 'yuno_display_name', sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name']));
            
            if (!empty($instructorData['phone'])) {
                update_user_meta($user_id, 'yuno_gplus_mobile', sanitize_text_field($instructorData['phone']));
            }
        } else {
            $user_id = (int)$instructorData['user_id'];
        }
        
        // Validate user exists
        $userdata = get_userdata($user_id);
        if (empty($userdata)) {
            return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'User not found', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
        }
        
        // Assign instructor role
        $u = new \WP_User($user_id);
        $u->remove_role('SEO Manager');
        $u->add_role('um_instructor');
        
        // Set basic instructor meta
        update_user_meta($user_id, 'profile_privacy', "public");
        update_user_meta($user_id, 'zoom_user_status', "free");
        
        // Process instructor details
        if (!empty($instructorData['bio'])) {
            update_user_meta($user_id, 'Instructor_About', $instructorData['bio']);
        }
        
        if (!empty($instructorData['expertise']) && is_array($instructorData['expertise'])) {
            update_user_meta($user_id, 'Instructor_Expertise', implode(',', $instructorData['expertise']));
        }
        
        if (!empty($instructorData['country'])) {
            update_user_meta($user_id, 'Instructor_Country', $instructorData['country']);
            update_user_meta($user_id, 'yuno_user_address_country', $instructorData['country']);
        }
        
        if (!empty($instructorData['state'])) {
            update_user_meta($user_id, 'Instructor_State', $instructorData['state']);
            update_user_meta($user_id, 'yuno_user_address_state', $instructorData['state']);
        }
        
        if (!empty($instructorData['city'])) {
            update_user_meta($user_id, 'Instructor_City', $instructorData['city']);
            update_user_meta($user_id, 'yuno_user_address_city', $instructorData['city']);
        }
        
        if (!empty($instructorData['native_language'])) {
            update_user_meta($user_id, 'Native_Language', $instructorData['native_language']);
        }
        
        if (!empty($instructorData['fluent_in']) && is_array($instructorData['fluent_in'])) {
            update_user_meta($user_id, 'Fluent_In', implode(',', $instructorData['fluent_in']));
        }
        
        if (!empty($instructorData['understand']) && is_array($instructorData['understand'])) {
            update_user_meta($user_id, 'Instructor_Understand', implode(',', $instructorData['understand']));
        }
        
        if (!empty($instructorData['teaching_preference']) && is_array($instructorData['teaching_preference'])) {
            update_user_meta($user_id, 'yuno_user_teaching_preference', implode(',', $instructorData['teaching_preference']));
            update_user_meta($user_id, 'Teaching_Preference', implode(',', $instructorData['teaching_preference']));
        }
        
        if (!empty($instructorData['experience'])) {
            update_user_meta($user_id, 'Instructor_Experience', $instructorData['experience']);
        }
        
        if (!empty($instructorData['laptop_availability'])) {
            update_user_meta($user_id, 'yuno_user_laptop_availability', $instructorData['laptop_availability']);
            update_user_meta($user_id, 'Laptop_Availability', $instructorData['laptop_availability']);
        }
        
        if (!empty($instructorData['broadband_connection_availability'])) {
            update_user_meta($user_id, 'yuno_user_broadband_connection_availability', $instructorData['broadband_connection_availability']);
            update_user_meta($user_id, 'Broadband_Connection_Availability', $instructorData['broadband_connection_availability']);
        }
        
        if (!empty($instructorData['online_teaching_exp'])) {
            update_user_meta($user_id, 'yuno_user_online_teaching_exp', $instructorData['online_teaching_exp']);
            update_user_meta($user_id, 'Online_Teaching_Exp', $instructorData['online_teaching_exp']);
        }
        
        if (!empty($instructorData['dob'])) {
            update_user_meta($user_id, 'yuno_user_dob', $instructorData['dob']);
            update_user_meta($user_id, 'Instructor_DOB', $instructorData['dob']);
        }
        
        if (!empty($instructorData['can_teach']) && is_array($instructorData['can_teach'])) {
            update_user_meta($user_id, 'can_teach', implode(',', $instructorData['can_teach']));
        }
        
        if (isset($instructorData['yuno_user_whatsapp_check'])) {
            $whatsappCheck = $instructorData['yuno_user_whatsapp_check'] ? "yes" : "no";
            update_user_meta($user_id, 'yuno_user_whatsapp_check', $whatsappCheck);
        }
        
        if (!empty($instructorData['profile_image'])) {
            update_user_meta($user_id, 'googleplus_profile_img', $instructorData['profile_image']);
        }
        
        // Set instructor as active if specified
        $login_status = 'inactive';
        if (!empty($instructorData['is_active']) && $instructorData['is_active'] === true) {
            update_user_meta($user_id, 'is_completed_step_1', "yes");
            update_user_meta($user_id, 'is_completed_step_2', "yes");
            update_user_meta($user_id, 'is_completed_step_3', "yes");
            update_user_meta($user_id, 'account_login_status', 'active');
            $login_status = 'active';
        }
        
        // Get city name if city ID is set
        global $wpdb;
        $city_name = '';
        $city_id = get_user_meta($user_id, 'yuno_user_address_city', true);
        if (!empty($city_id)) {
            $city = $wpdb->get_row("SELECT name FROM wp_cities where id=$city_id");
            $city_name = $city ? $city->name : '';
        }
        
        // Prepare user object for Elasticsearch
        $user_obj = [
            "name" => get_user_meta($user_id, 'yuno_display_name', true),
            "email" => get_user_meta($user_id, 'yuno_gplus_email', true),
            "phone" => get_user_meta($user_id, 'yuno_gplus_mobile', true),
            "image" => get_user_meta($user_id, 'googleplus_profile_img', true)
        ];
        
        // Create the instructor event in Elasticsearch
        $instructorEvent = [
            "data" => [
                "details" => [
                    "user_id" => $user_id,
                    "record_id" => $user_id,
                    "event_type" => "instructorsignedup",
                    "event_label" => "Instructor signed up",  
                    "account_login_status" => $login_status,
                    "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                    "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                    "country" => get_user_meta($user_id, 'yuno_user_address_country', true),
                    "city_id" => $city_id,
                    "city_name" => $city_name,
                    "fluent_in" => get_user_meta($user_id, 'Fluent_In', true),
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "instructor_understand" => get_user_meta($user_id, 'Instructor_Understand', true),
                    "laptop_availability" => get_user_meta($user_id, 'yuno_user_laptop_availability', true),
                    "broadband_connection_availability" => get_user_meta($user_id, 'yuno_user_broadband_connection_availability', true),
                    "online_teaching_exp" => get_user_meta($user_id, 'yuno_user_online_teaching_exp', true),
                    "dob" => get_user_meta($user_id, 'yuno_user_dob', true),
                    "can_teach" => get_user_meta($user_id, 'can_teach', true),
                    "teaching_preference" => get_user_meta($user_id, 'yuno_user_teaching_preference', true),
                    "instructor_experience" => get_user_meta($user_id, 'Instructor_Experience', true),
                    "address_pin_code" => get_user_meta($user_id, 'yuno_user_address_pin_code', true),
                    "address_flat_house_number" => get_user_meta($user_id, 'yuno_user_address_flat_house_number', true),
                    "address_street" => get_user_meta($user_id, 'yuno_user_address_street', true),           
                    "address_landmark" => get_user_meta($user_id, 'yuno_user_address_landmark', true),
                    "address_state" => get_user_meta($user_id, 'yuno_user_address_state', true),
                    "is_featured" => false,
                    "is_completed_step_1" => get_user_meta($user_id, 'is_completed_step_1', true),
                    "is_completed_step_2" => get_user_meta($user_id, 'is_completed_step_2', true),
                    "is_completed_step_3" => !empty(get_user_meta($user_id, 'is_completed_step_3', true)) ? get_user_meta($user_id, 'is_completed_step_3', true) : "no",
                    "yuno_user_whatsapp_check" => get_user_meta($user_id, 'yuno_user_whatsapp_check', true),
                    "active_enrolments" => '',
                    "completed_enrolments" => '',
                    "live_classes_delivered" => '',
                    "attendance" => '',
                    "active_batches" => 0,
                    "learner_avg_class_rating" => 0,
                    "staff_avg_class_rating" => 0,
                    "reviews_count" => 0,
                    "vc_status" => 'free',
                    "avg_rating" => '',
                    "instructor_about" => get_user_meta($user_id, 'Instructor_About', true),
                    "expertise" => get_user_meta($user_id, 'Instructor_Expertise', true),
                    "mapped_courses" => [],
                    "mapped_categories" => [],
                    "working_hours" => []
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];
        
        // Post the instructor event to Elasticsearch
        post_elastic_event($instructorEvent);
        
        // Create basic details in Elasticsearch
        $details = [
            "properties" => [
                "details" => [
                    "user_id" => $user_id,
                    "user" => $user_obj,
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "role" => "instructor",
                ],
            ]
        ];
        
        $properties = $details['properties'];
        $curlPost['data'] = [
            "data" => $properties,
        ];
        
        \UserElasticSearch::update_signedup("basic_details", $curlPost);
        
        $reqquested_data = [
            "document_type" => "course",
            "fields" => ["mapped_instructor_ids" => $user_id]
        ];
        
        update_instructor_lang_es($reqquested_data);
        
        // Wait a moment for Elasticsearch to index the data
        sleep(1);
        
        // Fetch the actual data from Elasticsearch
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $user_id);
        
        // Prepare response with expected structure
        $response = [
            'code' => $codes["PUT_UPDATE"]["code"],
            'message' => 'Instructor created successfully',
            'user_id' => $user_id,
            'role' => 'um_instructor',
            'email' => get_user_meta($user_id, 'yuno_gplus_email', true),
            'is_active' => get_user_meta($user_id, 'account_login_status', true) === 'active',
            'elasticsearch' => [
                'index' => 'instructorsignedup',
                'id' => 'instructorsignedup-' . $user_id,
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'user' => $user_obj,
                            'native_language' => get_user_meta($user_id, 'Native_Language', true),
                            'role' => 'instructor'
                        ]
                    ]
                ]
            ]
        ];
        
        // Add actual Elasticsearch data if available
        if (!empty($instructorDataResponse) && $instructorDataResponse['status_code'] === 200) {
            $response['elasticsearch_actual'] = $instructorDataResponse['body']['_source'];
        }
        
        return $response;
    }

    /**
     * Checks the data saved in Elasticsearch for an instructor
     *
     * @param int $instructorId The ID of the instructor to check
     * @return array|false Returns the Elasticsearch data or false if not found
     */
    public function checkInstructorElasticsearchData($instructorId)
    {
        if (empty($instructorId)) {
            return false;
        }

        // Read from instructorsignedup index
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $instructorId);
        
        if ($instructorDataResponse['status_code'] !== 200) {
            return false;
        }

        return [
            'raw_data' => $instructorDataResponse['body']['_source'],
            'formatted_data' => [
                'user_id' => $instructorDataResponse['body']['_source']['data']['details']['user_id'] ?? null,
                'name' => $instructorDataResponse['body']['_source']['data']['details']['user']['name'] ?? null,
                'email' => $instructorDataResponse['body']['_source']['data']['details']['user']['email'] ?? null,
                'phone' => $instructorDataResponse['body']['_source']['data']['details']['user']['phone'] ?? null,
                'image' => $instructorDataResponse['body']['_source']['data']['details']['user']['image'] ?? null,
                'native_language' => $instructorDataResponse['body']['_source']['data']['details']['native_language'] ?? null,
                'role' => $instructorDataResponse['body']['_source']['data']['details']['role'] ?? null
            ]
        ];
    }

        /**
     * Fetches a paginated and filtered list of instructors.
     *
     * This method orchestrates the entire process, from authorization to final response formatting.
     * It uses a highly optimized two-pass architecture to ensure performance by fetching data in bulk
     * and only enriching the final, paginated set of results.
     *
     * @param array $filters The filters to apply to the instructor search.
     * @param array $filter Optional filter parameters including schema validation.
     * @return array The API response.
     */
    public function getInstructors($filters = [], $filter = [])
    {
        // 1. Authorization: Check if the requesting user has permission.
        $authError = $this->performAuthorizationCheck($filters);
        if ($authError) {
            return $authError;
        }

        // 2. Parse and Sanitize Filters: Standardize all inputs for internal use.
        $parsedFilters = $this->parseAndValidateFilters($filters);

        // 3. Build and Execute Elasticsearch Query to get initial candidates.
        $esQuery = $this->buildElasticsearchQuery($parsedFilters);
        $esResults = $this->executeElasticsearchQuery($esQuery, 10000);

        if (!$esResults || empty($esResults['hits']['hits'])) {
            return $this->createEmptyResponse();
        }

        // 4. Extract and Pre-filter Candidates: Get a clean array of instructors
        // and immediately filter out any that don't have a valid WordPress user.
        $candidates = $this->extractCandidatesFromResults($esResults);
        $candidates = $this->filterCandidatesWithValidUsers($candidates);
        if (empty($candidates)) {
            return $this->createEmptyResponse();
        }

        // 5. Post-Filtering: Apply complex filters that must be run in PHP.
        $filteredCandidates = $this->performPostFiltering($candidates, $parsedFilters);
        if (empty($filteredCandidates)) {
            return $this->createEmptyResponse();
        }

        // 6. Sort: Sort the candidates by name before pagination.
        $sortedCandidates = $this->sortCandidatesByName($filteredCandidates);

        // 7. Paginate: Get the total count BEFORE slicing for the current page.
        $totalCount = count($sortedCandidates);
        $paginatedCandidates = array_slice($sortedCandidates, $parsedFilters['offset'], $parsedFilters['limit']);

        // 8. Enrich Data (The N+1 Fix): Perform expensive data fetching ONLY on the paginated subset.
        $enrichedInstructors = $this->enrichPaginatedInstructors($paginatedCandidates, $parsedFilters, $filter);

        // 9. Format Final Response: Build the final API response structure.
        return $this->formatFinalResponse($enrichedInstructors, $totalCount, $parsedFilters['viewType']);
    }

    // --- PRIVATE HELPER METHODS FOR getInstructors ---

    /**
     * Checks if the user specified in filters has the required admin role.
     */
    private function performAuthorizationCheck(array $filters): ?array
    {
        if (empty($filters['user_id'])) {
            return null;
        }

        $userId = (int) $filters['user_id'];
        $userRole = $this->userModel->getUserRole($userId);

        if (!$userRole) {
            return $this->createErrorResponse(404, 'User not found for the provided user_id.');
        }

        $isOrgAdmin = $this->userModel->checkRole($userRole, $this->userModel->yn_Org_Admin);
        $isYunoAdmin = $this->userModel->checkRole($userRole, $this->userModel->yn_Yuno_Admin);

        if ($isOrgAdmin === false && $isYunoAdmin === false) {
            return $this->createErrorResponse(403, 'Access Denied: User does not have the required role.');
        }
        return null;
    }

    /**
     * Parses, validates, and standardizes the input filter array.
     */
    private function parseAndValidateFilters(array $filters): array
    {
        $booleanOrNull = fn ($key) => isset($filters[$key]) ? filter_var($filters[$key], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
        
        $parsed = [
            'status' => $filters['status'] ?? null,
            'vc_status' => $filters['vc_status'] ?? null,
            'native_language' => $filters['native_language'] ?? null,
            'avg_rating' => (float) ($filters['avg_rating'] ?? 0),
            'course_id' => (int) ($filters['course_id'] ?? 0),
            'category_id' => (int) ($filters['category_id'] ?? 0),
            'org_id' => (int) ($filters['org_id'] ?? 0),
            'viewType' => $filters['viewType'] ?? 'grid',
            'is_disabled' => $booleanOrNull('is_disabled'),
            'is_featured' => $booleanOrNull('is_featured'),
            'has_active_batches' => $booleanOrNull('has_active_batches'),
            'has_past_batches' => $booleanOrNull('has_past_batches'),
            'has_mapped_courses' => $booleanOrNull('has_mapped_courses'),
            'has_working_hours' => $booleanOrNull('has_working_hours'),
            'has_active_enrollments' => $booleanOrNull('has_active_enrollments'),
            'has_past_enrollments' => $booleanOrNull('has_past_enrollments'),
            'category_ids' => $this->parseIdList($filters['category_ids'] ?? []),
            'mapped_courses' => $this->parseIdList($filters['mapped_courses'] ?? []),
        ];

        if (isset($filters['limit']) && isset($filters['offset'])) {
            $parsed['limit'] = (int) $filters['limit'];
            $parsed['offset'] = (int) $filters['offset'];
        } else {
            $parsed['limit'] = YN_DEFAULT_LIMIT;
            $parsed['offset'] = YN_DEFAULT_OFFSET;
        }

        return $parsed;
    }

    /**
     * Helper to parse a comma-separated string or array of IDs into a clean integer array.
     */
    private function parseIdList($value): array
    {
        if (is_array($value)) {
            return array_filter(array_map('intval', $value));
        }
        if (is_string($value) && !empty($value)) {
            return array_filter(array_map('intval', explode(',', trim($value, '[] '))));
        }
        return [];
    }

    /**
     * Builds the Elasticsearch query body based on parsed filters, preserving original logic.
     */
    private function buildElasticsearchQuery(array $parsedFilters): array
    {
        $must = [];
        if ($parsedFilters['is_disabled'] === true) {
            $must[] = ["bool" => ["must_not" => [["match" => ["data.details.account_login_status.keyword" => 'active']]]]];
        }
        if ($parsedFilters['status']) {
            $statusValue = ($parsedFilters['status'] === 'disabled') ? 'de-active' : $parsedFilters['status'];
            $must[] = ["match" => ["data.details.account_login_status.keyword" => $statusValue]];
        }
        if ($parsedFilters['vc_status'] && $parsedFilters['vc_status'] !== 'all') {
            $must[] = ["match" => ["data.details.vc_status" => $parsedFilters['vc_status']]];
        }
        if ($parsedFilters['native_language'] && $parsedFilters['native_language'] !== 'all') {
            $must[] = ["match" => ["data.details.native_language" => urldecode($parsedFilters['native_language'])]];
        }
        if ($parsedFilters['course_id'] > 0) {
            $must[] = ["term" => ["data.details.mapped_courses" => $parsedFilters['course_id']]];
        }
        if ($parsedFilters['category_id'] > 0) {
            $must[] = ["term" => ["data.details.mapped_categories" => $parsedFilters['category_id']]];
        }
        if (!empty($parsedFilters['category_ids'])) {
            $should = array_map(fn ($id) => ["term" => ["data.details.mapped_categories" => $id]], $parsedFilters['category_ids']);
            $must[] = ["bool" => ["should" => $should, "minimum_should_match" => count($should)]];
        }
        if (!empty($parsedFilters['mapped_courses'])) {
            $must[] = ["terms" => ["data.details.mapped_courses" => $parsedFilters['mapped_courses']]];
        }
        if ($parsedFilters['is_featured'] === true) {
            $must[] = ["bool" => ["should" => [["term" => ["data.details.is_featured" => true]], ["term" => ["data.details.is_featured" => "true"]], ["term" => ["data.details.is_featured" => 1]], ["term" => ["data.details.is_featured" => "1"]]], "minimum_should_match" => 1]];
        }
        if ($parsedFilters['avg_rating'] > 0) {
            $must[] = ["range" => ["data.details.avg_rating" => ["gte" => $parsedFilters['avg_rating']]]];
        }
        if ($parsedFilters['has_mapped_courses'] === true) {
            $must[] = ["exists" => ["field" => "data.details.mapped_courses"]];
        }

        return [
            "query" => ["bool" => ["must" => $must]],
            "_source" => ["data.details.user_id", "data.details.first_name", "data.details.last_name", "data.details.mapped_courses", "data.details.mapped_categories", "data.details.working_hours", "data.details.account_login_status", "data.details.native_language", "data.details.learner_avg_class_rating", "data.details.reviews_count", "data.details.vc_status"]
        ];
    }

    /**
     * Executes the Elasticsearch query using the ES library.
     */
    private function executeElasticsearchQuery(array $queryBody, int $size): ?array
    {
        if (!isset($queryBody['size'])) {
            $queryBody['size'] = $size;
        }
        
        $response = $this->es->customQuery($queryBody, 'instructorsignedup');
        
        if (!$response || $response['status_code'] !== 200) {
            error_log('Elasticsearch Error in getInstructors: ' . ($response['message'] ?? 'Unknown error'));
            return null;
        }
        
        return $response['body'];
    }

    /**
     * Extracts a simplified array of candidate instructors from ES results.
     */
    private function extractCandidatesFromResults(array $esResults): array
    {
        $candidates = [];
        foreach ($esResults['hits']['hits'] as $hit) {
            if (!empty($hit['_source']['data']['details']['user_id'])) {
                $candidates[] = [
                    'user_id' => (int) $hit['_source']['data']['details']['user_id'],
                    'first_name' => $hit['_source']['data']['details']['first_name'] ?? '',
                    'last_name' => $hit['_source']['data']['details']['last_name'] ?? '',
                    'es_details' => $hit['_source']['data']['details']
                ];
            }
        }
        return $candidates;
    }

    /**
     * Filters out candidates who do not have a corresponding valid user in WordPress.
     */
    private function filterCandidatesWithValidUsers(array $candidates): array
    {
        if (empty($candidates)) {
            return [];
        }
        $userIds = array_column($candidates, 'user_id');
        $users = get_users(['include' => $userIds, 'fields' => 'ID']);
        $validUserIds = array_flip($users); // Use array_flip for O(1) lookups
        return array_filter($candidates, fn ($candidate) => isset($validUserIds[$candidate['user_id']]));
    }

    /**
     * Applies complex filters that must be run in PHP after the main ES query.
     */
    private function performPostFiltering(array $candidates, array $parsedFilters): array
    {
        if (empty($candidates)) {
            return [];
        }

        $userIds = array_column($candidates, 'user_id');

        $batchCounts = [];
        if ($parsedFilters['has_active_batches'] !== null || $parsedFilters['has_past_batches'] !== null) {
            $batchCounts['active'] = $this->getBatchCountsForInstructors($userIds, true);
            $batchCounts['past'] = $this->getBatchCountsForInstructors($userIds, false);
        }

        $affiliationData = [];
        if ($parsedFilters['org_id'] > 0) {
            $affiliationData = $this->fetchAggregatedAffiliationData($userIds);
        }

        return array_filter($candidates, function ($candidate) use ($parsedFilters, $batchCounts, $affiliationData) {
            $userId = $candidate['user_id'];
            $esDetails = $candidate['es_details'];

            if ($parsedFilters['has_active_batches'] === true) {
                if (empty($this->getLinkedBatchesInfoFromCourses($userId, $esDetails['mapped_courses'] ?? []))) {
                    return false;
                }
            }
            if ($parsedFilters['has_active_batches'] === false) {
                if (($batchCounts['active'][$userId] ?? 0) > 0) {
                    return false;
                }
            }

            if ($parsedFilters['has_past_batches'] === true && ($batchCounts['past'][$userId] ?? 0) === 0) {
                return false;
            }
            if ($parsedFilters['has_past_batches'] === false && ($batchCounts['past'][$userId] ?? 0) > 0) {
                return false;
            }

            // **FIXED**: This check now correctly mirrors the original's PHP-only filtering logic.
            if ($parsedFilters['has_working_hours'] === true) {
                if (empty($esDetails['working_hours'])) {
                    return false;
                }
            }
            
            if ($parsedFilters['has_mapped_courses'] === true) {
                 if (empty($esDetails['mapped_courses'])) {
                    return false;
                }
            }

            if ($parsedFilters['org_id'] > 0) {
                if (!$this->isInstructorInOrg($userId, $parsedFilters['org_id'], $affiliationData)) {
                    return false;
                }
            }

            if ($parsedFilters['has_active_enrollments'] === true || $parsedFilters['has_past_enrollments'] === true) {
                $enrollmentBatches = $this->getBatchesForInstructorFromCourses($userId, $esDetails['mapped_courses'] ?? []);
                if ($parsedFilters['has_active_enrollments'] === true && !$this->hasFutureBatches($enrollmentBatches)) {
                    return false;
                }
                if ($parsedFilters['has_past_enrollments'] === true && !$this->hasPastBatches($enrollmentBatches)) {
                    return false;
                }
            }

            return true;
        });
    }

    private function hasFutureBatches(array $batchInfo): bool
    {
        $now = new \DateTime();
        foreach ($batchInfo as $batch) {
            if (!empty($batch['batch_end_date'])) {
                try {
                    if (new \DateTime($batch['batch_end_date']) >= $now) return true;
                } catch (\Exception $e) {}
            }
        }
        return false;
    }

    private function hasPastBatches(array $batchInfo): bool
    {
        $now = new \DateTime();
        foreach ($batchInfo as $batch) {
            if (!empty($batch['batch_end_date'])) {
                try {
                    if (new \DateTime($batch['batch_end_date']) < $now) return true;
                } catch (\Exception $e) {}
            }
        }
        return false;
    }

    private function isInstructorInOrg(int $instructorId, int $targetOrgId, array $affiliationData): bool
    {
        $courses = $affiliationData['instructor_to_mapped_courses'][$instructorId] ?? [];
        foreach ($courses as $courseId) {
            $academies = $affiliationData['course_to_academies'][(string) $courseId] ?? [];
            foreach ($academies as $academyId) {
                if (((int) ($affiliationData['academy_to_org'][(string) $academyId] ?? 0)) === $targetOrgId) {
                    return true;
                }
            }
        }
        return false;
    }

    private function sortCandidatesByName(array $candidates): array
    {
        usort($candidates, fn ($a, $b) => strcasecmp(trim("{$a['first_name']} {$a['last_name']}"), trim("{$b['first_name']} {$b['last_name']}")));
        return $candidates;
    }

    private function enrichPaginatedInstructors(array $paginatedCandidates, array $parsedFilters, array $filter = []): array
    {
        if (empty($paginatedCandidates)) {
            return [];
        }
        $userIds = array_column($paginatedCandidates, 'user_id');
        $usersById = [];
        $userMeta = [];
        if (!empty($userIds)) {
            $users = get_users(['include' => $userIds, 'fields' => ['ID', 'user_email', 'roles']]);
            update_meta_cache('user', $userIds);
            foreach ($users as $user) {
                $usersById[$user->ID] = $user;
                $userMeta[$user->ID] = get_user_meta($user->ID);
            }
        }

        $enrichedData = [];
        foreach ($paginatedCandidates as $candidate) {
            $userId = $candidate['user_id'];
            if (!isset($usersById[$userId])) {
                continue;
            }

            $user = $usersById[$userId];
            $meta = $userMeta[$userId] ?? [];
            $esDetails = $candidate['es_details'];
            $firstName = $meta['yuno_first_name'][0] ?? $candidate['first_name'];
            $lastName = $meta['yuno_last_name'][0] ?? $candidate['last_name'];
            $nativeLang = $esDetails['native_language'] ?? 'English';
            
            // Generate profile URL following the same pattern as other controllers
            $profileUserIdReference = $meta['profile_user_id_reference'][0] ?? '';
            $profileUrl = '';
            if (!empty($profileUserIdReference)) {
                $profileUrl = get_permalink($profileUserIdReference);
            }
            // Fallback to name-based URL if no profile reference found or permalink failed
            if (empty($profileUrl) || $profileUrl === false) {
                $profileUrl = site_url("/profile/" . strtolower($firstName) . "-" . strtolower($lastName));
            }

            // Prepare current_state data similar to getUserInfo API
            $hasPhoneNumber = !empty($meta['yuno_gplus_mobile'][0] ?? '');

            $activeCategorySlug = $meta['active_category'][0] ?? '';
            $activeCategoryObj = [
                'id' => 0,
                'name' => '',
                'slug' => '',
            ];
            if (!empty($activeCategorySlug)) {
                $term = get_term_by('slug', $activeCategorySlug, 'course_category');
                if ($term !== false && !is_wp_error($term)) {
                    $activeCategoryObj = [
                        'id' => (int) $term->term_id,
                        'name' => $term->name,
                        'slug' => $term->slug,
                    ];
                }
            }

            $definedOrg = 0;
            $organisations = $meta['organisation'][0] ?? '';
            if (!empty($organisations)) {
                $organisations = maybe_unserialize($organisations);
                if (is_array($organisations) && count($organisations) == 1) {
                    $definedOrg = (int)$organisations[0];
                }
            }
            $activeOrg = $meta['active_org'][0] ?? '';
            $activeOrgId = !empty($activeOrg) ? (int)$activeOrg : $definedOrg;
            if ($activeOrgId <= 0 && $definedOrg > 0) {
                update_user_meta($userId, 'active_org', $definedOrg);
                $activeOrgId = $definedOrg;
            }

            $redirectUrl = $meta['redirect_url'][0] ?? '';

            $currentStateObj = [
                "has_phone_number" => $hasPhoneNumber,
                "active_category" => $activeCategoryObj,
                "org_id" => (int)$activeOrgId,
                "redirect_url" => $redirectUrl,
            ];

            // Get mapped courses data with id, title, and url
            $courseData = [];
            if (!empty($esDetails['mapped_courses']) && is_array($esDetails['mapped_courses'])) {
                foreach ($esDetails['mapped_courses'] as $courseId) {
                    try {
                        $course = $this->courseModel->getCourse(['id' => $courseId], ['schema' => 'Course_Minimal']);
                        
                        // Detailed validation of course data
                        if ($course && is_array($course) && isset($course['id'], $course['title'], $course['url'])) {
                            $courseData[] = $course;
                        } else {
                            error_log("Course {$courseId} failed validation - data: " . json_encode($course));
                        }
                    } catch (\Exception $e) {
                        // Log the error but continue processing other courses
                        error_log("Failed to load course ID {$courseId} for instructor {$userId}: " . $e->getMessage());
                    }
                }
            }

            // Handle empty native language gracefully
            $nativeLanguage = !empty($nativeLang) ? $nativeLang : 'English';
            $nativeLanguageCode = !empty($nativeLang) ? substr(strtolower($nativeLang), 0, 2) : 'en';

            // Check if we're using the basic schema
            $isBasicSchema = function_exists('ynSchemaType') && ynSchemaType($filter, 'Instructor_Basic');
            
            $userDetails = $this->userModel->getUser($userId, ['schema' => 'User_Minimal']);

            if ($isBasicSchema) {
                // Build data specifically for Instructor_Basic schema
                $instructorRawData = [
                    "user" => $userDetails,
                    "native_language" => [
                        "name_in_english" => $nativeLanguage, 
                        "native_lang_name" => $nativeLanguage, 
                        "code" => $nativeLanguageCode
                    ],
                    "has_mapped_courses" => !empty($courseData),
                    "avg_rating" => (float) ($esDetails['learner_avg_class_rating'] ?? 0),
                    "review_count" => (int) ($esDetails['reviews_count'] ?? 0),
                    "has_working_hours" => (!empty($esDetails['working_hours']) && is_array($esDetails['working_hours'])),
                ];
                
                // Add required fields for Instructor_Basic schema
                $instructorRawData["about"] = !empty($meta['Instructor_About'][0]) ? $meta['Instructor_About'][0] : false;
                $instructorRawData["profile_url"] = !empty($profileUrl) ? $profileUrl : false;
                $instructorRawData["mapped_courses"] = !empty($courseData) ? $courseData : false;
                
                // Add fluent_in_languages - basic schema expects this field
                $instructorRawData["fluent_in_languages"] = [[
                    "name_in_english" => $nativeLanguage, 
                    "native_lang_name" => $nativeLanguage, 
                    "code" => $nativeLanguageCode
                ]];
                
                // Add required fields with default values
                $instructorRawData["max_rating"] = 5.0;  // Set a reasonable max rating
                $instructorRawData["active_learners"] = 0;
                $instructorRawData["past_learners"] = 0;
                $instructorRawData["current_state"] = $currentStateObj;
            } else {
                // Full instructor schema data
                $instructorRawData = [
                    "user" => $userDetails,
                    "about" => !empty($meta['Instructor_About'][0]) ? $meta['Instructor_About'][0] : false,
                    "profile_url" => $profileUrl,
                    "native_language" => [
                        "name_in_english" => $nativeLanguage, 
                        "native_lang_name" => $nativeLanguage, 
                        "code" => $nativeLanguageCode
                    ],
                    "fluent_in_languages" => [[
                        "name_in_english" => $nativeLanguage, 
                        "native_lang_name" => $nativeLanguage, 
                        "code" => $nativeLanguageCode
                    ]],
                    "has_mapped_courses" => !empty($courseData),
                    "mapped_courses" => $courseData,
                    "avg_rating" => (float) ($esDetails['learner_avg_class_rating'] ?? 0),
                    "max_rating" => 5.0,
                    "review_count" => (int) ($esDetails['reviews_count'] ?? 0),
                    "active_learners" => 0,
                    "past_learners" => 0,
                    "current_state" => $currentStateObj,
                    "has_working_hours" => (!empty($esDetails['working_hours']) && is_array($esDetails['working_hours'])),
                ];
            }

            if (!empty($courseData)) {
                $instructorRawData['mapped_courses'] = $courseData;
            }

            // Apply schema validation to individual instructor data
            $schemaType = 'Instructor_Basic';
            $validatedInstructorData = $this->schema->validate($instructorRawData, $schemaType, $filter);

            // Handle mapped_courses field appropriately for each schema type
            if (!$isBasicSchema) {
                // For full schema, set to false if validation issues
                if (!isset($validatedInstructorData['mapped_courses']) || !is_array($validatedInstructorData['mapped_courses']) || (isset($validatedInstructorData['mapped_courses'][0]) && !is_array($validatedInstructorData['mapped_courses'][0]))) {
                    $validatedInstructorData['mapped_courses'] = false;
                }
            } else {
                // For basic schema, ensure mapped_courses is either array or not present
                if (isset($validatedInstructorData['mapped_courses']) && !is_array($validatedInstructorData['mapped_courses'])) {
                    unset($validatedInstructorData['mapped_courses']);
                }
            }

            // Remove the "instructor" wrapper - return instructor data directly
            $instructor_data = $validatedInstructorData;

            if ($parsedFilters['has_active_enrollments'] !== null || $parsedFilters['has_past_enrollments'] !== null) {
                // $instructor_data['linked_batch_info'] = $this->getBatchesForInstructorFromCourses($userId, $esDetails['mapped_courses'] ?? []);
            }
            $enrichedData[] = $instructor_data;
        }
        return $enrichedData;
    }

    private function formatFinalResponse(array $data, int $totalCount, string $viewType): array
    {
        // For non-list view, structure the response with rows containing instructor data directly
        if ($viewType === 'list') {
            $responseData = $data;
        } else {
            $responseData = [
                'rows' => $data,
                'columns' => [
                    ["field" => "name", "label" => "Name"],
                    ["field" => "can_schedule_class", "label" => "VC Status"],
                    ["field" => "native_lang", "label" => "Native Lang"],
                    ["field" => "learner_avg_class_rating", "label" => "Learner avg. class rating"],
                    ["field" => "reviews_count", "label" => "Review"]
                ]
            ];
        }
        return ['code' => 0, 'message' => 'Instructor insights has been fetched successfully', 'status' => 'success', 'count' => $totalCount, 'data' => $responseData];
    }

    private function createEmptyResponse(): array
    {
        return ['code' => 200, 'message' => 'No Data Found', 'status' => 'success', 'count' => 0, 'data' => []];
    }

    private function createErrorResponse(int $code, string $message): array
    {
        return ['code' => $code, 'message' => $message, 'status' => 'FAIL', 'count' => 0, 'data' => []];
    }

    /**
     * Get batch counts for multiple instructors in a single query
     * This method replaces individual queries with a single batch query
     * 
     * @param array $instructorIds Array of instructor user IDs
     * @param bool $isActive Whether to count active or past batches
     * @return array Associative array of instructor_id => batch_count
     */
    private function getBatchCountsForInstructors($instructorIds, $isActive = true)
    {
        if (empty($instructorIds)) {
            return [];
        }
        
        // Current date for filtering
        $currentDateTime = date("Y-m-d H:i:s");
        
        // Build Elasticsearch query for batches
        $timeRangeOperator = $isActive ? "gt" : "lt";
        
        $must = [
            [
                "terms" => [
                    "data.details.batch_details.instructor_id" => $instructorIds
                ]
            ]
        ];
        
        // For active batches, add additional filters
        if ($isActive) {
            $must[] = [
                "match" => [
                    "data.details.batch_details.active_batch" => 1
                ]
            ];
            $must[] = [
                "match" => [
                    "data.details.batch_details.batch_deleted_status" => 0
                ]
            ];
        }
        
        // Add date range filter
        $must[] = [
            "range" => [
                "data.details.batch_details.batch_end_date" => [
                    $timeRangeOperator => $currentDateTime
                ]
            ]
        ];
        
        $curlPost = [
            "_source" => [
                "data.details.batch_details.instructor_id",
                "data.details.batch_details.batch_id"
            ],
            "query" => [
                "nested" => [
                    "path" => "data.details.batch_details",
                    "query" => [
                        "bool" => [
                            "must" => $must
                        ]
                    ],
                    "inner_hits" => [
                        "size" => 10000, // Set a high limit to ensure all batches are returned
                        "_source" => ["instructor_id", "batch_id"]
                    ]
                ]
            ],
            "size" => 1000 // We only need the aggregation results
        ];
        
        // Execute Elasticsearch query
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "course/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            error_log("Error fetching batch counts: " . $err);
            return [];
        }
        
        $results = json_decode($response, true);
        
        // Initialize counts array with zeros for all instructors
        $batchCounts = array_fill_keys($instructorIds, 0);
        
        // Process results to count batches per instructor
        if (!empty($results['hits']['hits'])) {
            $key = "data.details.batch_details";
            
            foreach ($results['hits']['hits'] as $hit) {
                if (empty($hit['inner_hits'][$key]['hits']['hits'])) {
                    continue;
                }
                
                $innerHits = $hit['inner_hits'][$key]['hits']['hits'];
                
                foreach ($innerHits as $innerHit) {
                    $instructorId = $innerHit['_source']['instructor_id'] ?? null;
                    
                    if ($instructorId && in_array($instructorId, $instructorIds)) {
                        $batchCounts[$instructorId]++;
                    }
                }
            }
        }
        
        return $batchCounts;
    }

    private function fetchAggregatedAffiliationData(array $instructorUserIds, ?string $filterOrgIdParam_UNUSED = null)
    {
        $results = [
            'instructor_to_mapped_courses' => [],
            'course_to_academies' => [],
            'academy_to_org' => [],
            // 'all_unique_org_details' => [], // We might not need to fetch full org details here, only IDs
        ];

        if (empty($instructorUserIds)) {
            return $results;
        }

        // 1. Fetch Mapped Courses for all instructors
        $instructorDocsToFetch = [];
        foreach ($instructorUserIds as $id) {
            $instructorDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'instructorsignedup', '_id' => 'instructorsignedup-' . (string)$id, '_source' => ['data.details.mapped_courses']];
        }

        $allCourseIdsFlatUnique = [];
        if (!empty($instructorDocsToFetch)) {
            $mgetBody = ['docs' => $instructorDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $instructorId = (int)str_replace('instructorsignedup-', '', $doc['_id']);
                            $courses = $doc['_source']['data']['details']['mapped_courses'] ?? [];
                            $results['instructor_to_mapped_courses'][$instructorId] = $courses;
                            foreach ($courses as $courseId) {
                                if (!empty($courseId)) $allCourseIdsFlatUnique[(string)$courseId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allCourseIdsFlatUnique = array_keys($allCourseIdsFlatUnique);

        if (empty($allCourseIdsFlatUnique)) return $results;

        // 2. Fetch Course-to-Academy Mappings
        $courseDocsToFetch = [];
        foreach($allCourseIdsFlatUnique as $cid) {
             $courseDocsToFetch[] = 'course-'. (string)$cid;
        }
        
        $allAcademyIdsFlatUnique = [];
        if(!empty($courseDocsToFetch)){
            $mgetBody = ['ids' => $courseDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $academies = $doc['_source']['data']['details']['academies'] ?? [];
                            $results['course_to_academies'][$courseId] = $academies;
                            foreach ($academies as $academyId) {
                                if(!empty($academyId)) $allAcademyIdsFlatUnique[(string)$academyId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allAcademyIdsFlatUnique = array_keys($allAcademyIdsFlatUnique);

        if (empty($allAcademyIdsFlatUnique)) return $results;

        // 3. Fetch Academy-to-Org Mappings
        $academyDocsToFetch = [];
        foreach($allAcademyIdsFlatUnique as $aid){ 
            $academyDocsToFetch[] = 'academies-' . (string)$aid;
        }

        if(!empty($academyDocsToFetch)){
            $mgetBody = ['ids' => $academyDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $academyDetails = $doc['_source']['data']['details'] ?? [];
                            $orgId = $academyDetails['org_id'] ?? '';
                            if (!empty($orgId) && $orgId !== 'undefined') {
                                $orgIdStr = (string)$orgId;
                                $results['academy_to_org'][$academyId] = $orgIdStr;
                                // We don't need to collect all_unique_org_details here for now
                                // Details will be fetched on demand or if absolutely necessary later
                            }
                        }
                    }
                }
            }
        }
        return $results;
    }

    // [START RESTORE METHOD - getInstructorMappedCoursesAcademies]
    private function getInstructorMappedCoursesAcademies($instructorId, $requestedOrgId = null)
    {
        if (empty($instructorId)) {
            return [
                'mapped_courses' => [],
                'course_academies' => [],
                'academy_orgs' => [],
                'organization_data' => []
            ];
        }

        // Step 1: Get mapped courses from instructor document
        $instructorDataResponse = ['status_code' => 0, 'body' => []]; 
        if (isset($this->es)) {
            // This line might cause a linter error if $this->es is not defined, but restoring original structure.
            $instructorDataResponse = $this->es->read(YN_ES_PREFIX . 'instructorsignedup', 'instructorsignedup-' . $instructorId);
        }
        
        $mappedCourseIds = [];
        if ($instructorDataResponse['status_code'] === 200) {
            $instructorDetails = $instructorDataResponse['body']['_source']['data']['details'] ?? [];
            if (!empty($instructorDetails['mapped_courses'])) {
                $mappedCourseIds = $instructorDetails['mapped_courses'];
            }
        }
            
        if (empty($mappedCourseIds)) {
            return [
                'mapped_courses' => [], 
                'course_academies' => [],
                'academy_orgs' => [],
                'organization_data' => []
            ];
        }

        $courseAcademies = [];
        $validCourseIds = []; 
        $academyIdsLookup = []; 
        
        $courseIdsForQuery = array_map(function($id) {
            return 'course-' . $id;
        }, $mappedCourseIds);
        
        if (!empty($courseIdsForQuery)) {
            $mgetBody = ['ids' => $courseIdsForQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);
            
            if (!$err && $response) {
                $coursesData = json_decode($response, true);
                if (!empty($coursesData['docs'])) {
                    foreach ($coursesData['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $courseDetails = $doc['_source']['data']['details'] ?? [];
                            if (!empty($courseDetails['academies'])) {
                                $courseAcademies[$courseId] = $courseDetails['academies'];
                                $validCourseIds[] = $courseId; 
                                foreach ($courseDetails['academies'] as $academyId) { 
                                    if(!empty($academyId)) $academyIdsLookup[(string)$academyId] = true; 
                                }
                            }
                        }
                    }
                }
            }
        }
        
        $academyIds = array_keys($academyIdsLookup);
        $academyOrgs = []; 
        $orgIdsLookup = []; 

        if (!empty($academyIds)) {
            $academyDocIds = array_map(function($id) { return 'academies-' . $id; }, $academyIds);
            $mgetBody = ['ids' => $academyDocIds];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);
            
            if (!$err && $response) {
                $academiesData = json_decode($response, true);
                if (!empty($academiesData['docs'])) {
                    foreach ($academiesData['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $academyDetails = $doc['_source']['data']['details'] ?? [];
                            $orgId = $academyDetails['org_id'] ?? '';
                            if (!empty($orgId) && $orgId !== 'undefined') {
                                $orgIdStr = (string)$orgId;
                                $academyOrgs[$academyId] = $orgIdStr;
                                $orgIdsLookup[$orgIdStr] = true;
                            }
                        }
                    }
                }
            }
        }
        $uniqueOrgIdsFromAcademies = array_keys($orgIdsLookup);
        
        $finalOrganizationData = [];
        if (!empty($requestedOrgId)) {
            $orgUserMetaResult = get_user_meta($requestedOrgId, 'organisation', true);
            if (!empty($orgUserMetaResult) && is_array($orgUserMetaResult)) {
                $finalOrganizationData = array_map('strval', $orgUserMetaResult);
            } elseif (!empty($orgUserMetaResult)) { 
                $finalOrganizationData = [(string)$orgUserMetaResult];
            } else {
                $finalOrganizationData = [(string)$requestedOrgId];
            }
        } else {
            $finalOrganizationData = array_map('strval', $uniqueOrgIdsFromAcademies);
        }
        $finalOrganizationData = array_values(array_unique($finalOrganizationData));

        return [
            'mapped_courses' => $mappedCourseIds, 
            'course_academies' => $courseAcademies, 
            'academy_orgs' => $academyOrgs, 
            'organization_data' => $finalOrganizationData
        ];
    }
    // [END RESTORE METHOD]

    private function getBatchedAffiliationDataForOutput(array $instructorUserIds)
    {
        $affiliationData = [
            'instructor_course_map' => [], // instructor_id => [course_ids]
            'course_academy_map' => [],  // course_id => [academy_ids]
            'academy_org_map' => [],     // academy_id => org_id (string)
        ];

        if (empty($instructorUserIds)) {
            return $affiliationData;
        }

        // 1. Get mapped_courses for all instructors
        $instructorDocsToFetch = [];
        foreach ($instructorUserIds as $id) {
            $instructorDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'instructorsignedup', '_id' => 'instructorsignedup-' . (string)$id, '_source' => ['data.details.mapped_courses']];
        }

        $allCourseIdsUnique = []; // Store unique course_ids to fetch their academies
        if (!empty($instructorDocsToFetch)) {
            $mgetBody = ['docs' => $instructorDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $instructorId = (int)str_replace('instructorsignedup-', '', $doc['_id']);
                            $courses = $doc['_source']['data']['details']['mapped_courses'] ?? [];
                            $affiliationData['instructor_course_map'][$instructorId] = $courses;
                            foreach ($courses as $courseId) {
                                if (!empty($courseId)) $allCourseIdsUnique[(string)$courseId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allCourseIdsUnique = array_keys($allCourseIdsUnique);
        if (empty($allCourseIdsUnique)) return $affiliationData; // No courses, nothing more to fetch

        // 2. Get academies for all unique courses
        $courseDocsForAcademyQuery = array_map(function ($cid) { return 'course-' . $cid; }, $allCourseIdsUnique);
        $allAcademyIdsUnique = []; // Store unique academy_ids to fetch their orgs
        if (!empty($courseDocsForAcademyQuery)) {
            $mgetBody = ['ids' => $courseDocsForAcademyQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $academies = $doc['_source']['data']['details']['academies'] ?? [];
                            $affiliationData['course_academy_map'][$courseId] = $academies;
                            foreach ($academies as $academyId) {
                                if (!empty($academyId)) $allAcademyIdsUnique[(string)$academyId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allAcademyIdsUnique = array_keys($allAcademyIdsUnique);
        if (empty($allAcademyIdsUnique)) return $affiliationData; // No academies, nothing more to fetch

        // 3. Get org_id for all unique academies
        $academyDocsForOrgQuery = array_map(function ($aid) { return 'academies-' . $aid; }, $allAcademyIdsUnique);
        if (!empty($academyDocsForOrgQuery)){
            $mgetBody = ['ids' => $academyDocsForOrgQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $orgId = $doc['_source']['data']['details']['org_id'] ?? null;
                            if ($orgId && $orgId !== 'undefined') {
                                $affiliationData['academy_org_map'][$academyId] = (string)$orgId;
                            }
                        }
                    }
                }
            }
        }
        return $affiliationData;
    }

    /**
     * Checks if an instructor is linked to any batch within their mapped courses.
     * Relies on instructor_id match in batch_details, ignores active status.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return array An array of unique batch_ids the instructor is linked to, or an empty array.
     */
    private function getInstructorLinkedBatchIdsFromCourses($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [];
        }

        $linkedBatchIds = [];
        // Prepare course document IDs for mget
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            // Ensure courseId is scalar and not empty or zero if zero is invalid
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) { 
                $courseDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return $linkedBatchIds;
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in getInstructorLinkedBatchIdsFromCourses: ' . $err);
            return $linkedBatchIds; // Or handle error as appropriate
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    // return true; // Found a link -> OLD LOGIC
                                    $linkedBatchIds[] = (string)$batch['batch_id'];
                                }
                            }
                        }
                    }
                }
            }
        }
        // return false; // No link found -> OLD LOGIC
        return array_unique($linkedBatchIds); // Return unique batch IDs
    }

    /**
     * Checks if an instructor has past batches based on batch end dates from batchenrollmentevent.
     * An instructor is considered to have past batches if they are linked to a batch
     * (via mapped_courses -> course.batch_details -> batch.instructor_id)
     * and that batch's end_date (from batchenrollmentevent) is in the past.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return bool True if past batches exist by this logic, false otherwise.
     */
    private function checkInstructorPastBatchesByEventEndDate($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return false;
        }

        // Step 1: Collect relevant batch_ids taught by the instructor from their mapped courses
        $relevantBatchIds = [];
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                $courseDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return false;
        }

        $mgetBodyCourses = ['docs' => $courseDocsToFetch];
        $curlCourses = curl_init();
        curl_setopt_array($curlCourses, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBodyCourses, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            CURLOPT_TIMEOUT => 30
        ]);
        $responseCourses = curl_exec($curlCourses);
        $errCourses = curl_error($curlCourses);
        curl_close($curlCourses);

        if ($errCourses) {
            error_log('ES mget error for courses in checkInstructorPastBatchesByEventEndDate: ' . $errCourses);
            return false;
        }

        if ($responseCourses) {
            $esResponseCourses = json_decode($responseCourses, true);
            if (!empty($esResponseCourses['docs'])) {
                foreach ($esResponseCourses['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetails = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetails)) {
                            foreach ($batchDetails as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $relevantBatchIds[] = (string)$batch['batch_id'];
                                }
                            }
                        }
                    }
                }
            }
        }
        
        $relevantBatchIds = array_unique($relevantBatchIds);
        if (empty($relevantBatchIds)) {
            return false; // No batches found for this instructor in their mapped courses
        }

        // Step 2: Check batch_end_date from batchenrollmentevent for these batch_ids
        $searchBodyEnrollmentEvents = [
            'query' => [
                'terms' => [
                    'data.details.batch_id' => $relevantBatchIds 
                ]
            ],
            '_source' => ['data.details.batch_end_date'],
            'size' => count($relevantBatchIds) 
        ];
        
        $curlEnrollments = curl_init();
        curl_setopt_array($curlEnrollments, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "batchenrollmentevent/_search", 
            CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($searchBodyEnrollmentEvents, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            CURLOPT_TIMEOUT => 30
        ]);
        $responseEnrollments = curl_exec($curlEnrollments);
        $errEnrollments = curl_error($curlEnrollments);
        curl_close($curlEnrollments);

        if ($errEnrollments) {
            error_log('ES search error for batchenrollmentevent in checkInstructorPastBatchesByEventEndDate: ' . $errEnrollments);
            return false;
        }
        
        if ($parsedFilters['has_active_batches'] === true) {
            if (($batchCounts['active'][$userId] ?? 0) === 0) {
                return false;
            }
        }
        if ($parsedFilters['has_active_batches'] === false) {
            if (($batchCounts['active'][$userId] ?? 0) > 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * Retrieves detailed information (batch_id, instructor_id from batch) for batches linked to an instructor 
     * through their mapped courses. Relies on instructor_id match in batch_details.
     *
     * @param int $mainInstructorId The ID of the main instructor being processed.
     * @param array $mappedCourseIds An array of course IDs mapped to the main instructor.
     * @return array An array of objects, each like {"batch_id": "...", "instructor_id": "..."}, or an empty array.
     */
    private function getLinkedBatchesInfoFromCourses($mainInstructorId, $mappedCourseIds)
    {
        if (empty($mainInstructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [];
        }

        $linkedBatchesInfo = [];
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                $courseDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return [];
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in getLinkedBatchesInfoFromCourses: ' . $err);
            return [];
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetails = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetails)) {
                            foreach ($batchDetails as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$mainInstructorId && isset($batch['batch_id'])) {
                                    $linkedBatchesInfo[] = [
                                        "batch_id" => (string)$batch['batch_id'],
                                        "instructor_id" => (string)$batch['instructor_id'] // This is the instructor_id from the batch_details object
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }
        return $linkedBatchesInfo;
    }

    /**
     * Retrieves all linked batch IDs and their source course IDs for an instructor.
     * This is similar to fetching active batches but without the active status check.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return array An array containing two elements: [0] => array of unique batch_ids, [1] => array of unique course_ids.
     */
    private function getInstructorLinkedBatchAndCourseIds($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [[], []]; // Return empty arrays for batch_ids and course_ids
        }

        $linkedBatchIds = [];
        $linkedCourseIdsForBatches = [];

        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                // We need to fetch data.details.batch_details and data.details.id (course_id itself for tracking)
                $courseDocsToFetch[] = ['_index' => YN_ES_PREFIX . 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details', 'data.details.id']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return [[], []];
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in getInstructorLinkedBatchAndCourseIds: ' . $err);
            return [[], []];
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        $sourceCourseId = $doc['_source']['data']['details']['id'] ?? null; // Get the course ID from the source

                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $linkedBatchIds[] = (string)$batch['batch_id'];
                                    if ($sourceCourseId) { // Only add course ID if it was found
                                        $linkedCourseIdsForBatches[] = (string)$sourceCourseId;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return [array_values(array_unique($linkedBatchIds)), array_values(array_unique($linkedCourseIdsForBatches))];
    }

    /**
     * Retrieves batch_id and instructor_id from batch_details for an instructor's mapped courses.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseRecordIds An array of course record_ids mapped to the instructor.
     * @return array An array of objects, each like {"batch_id": "...", "instructor_id": "..."}.
     */
    private function getBatchesForInstructorFromCourses($instructorId, $mappedCourseRecordIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseRecordIds) || empty($mappedCourseRecordIds)) {
            return [];
        }

        $initialBatchInfoFromCourses = [];
        $uniqueBatchIdsFromCourses = [];

        $courseDocIdsToFetch = array_map(function($recordId) {
            return 'course-' . $recordId;
        }, $mappedCourseRecordIds);

        if (empty($courseDocIdsToFetch)) {
            return [];
        }

        $mgetBodyCourses = [
            'docs' => array_map(function($docId) {
                // Request batch_details which includes batch_id, instructor_id, and potentially a fallback batch_end_date
                return ['_index' => YN_ES_PREFIX . 'course', '_id' => $docId, '_source' => ['data.details.batch_details']];
            }, $courseDocIdsToFetch)
        ];

        $curlCourses = curl_init();
        curl_setopt_array($curlCourses, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBodyCourses, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
        ]);
        $responseCourses = curl_exec($curlCourses);
        $errCourses = curl_error($curlCourses);
        curl_close($curlCourses);

        if ($errCourses) {
            error_log('Elasticsearch mget error for courses in _getBatchesForInstructorFromCourses: ' . $errCourses);
            return [];
        }

        if ($responseCourses) {
            $esResponseCourses = json_decode($responseCourses, true);
            if (!empty($esResponseCourses['docs'])) {
                foreach ($esResponseCourses['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $current_batch_id = (string)$batch['batch_id'];
                                    $initialBatchInfoFromCourses[] = [
                                        "batch_id" => $current_batch_id,
                                        "instructor_id" => (string)$batch['instructor_id'],
                                        "batch_end_date_from_course" => $batch['batch_end_date'] ?? null
                                    ];
                                    $uniqueBatchIdsFromCourses[$current_batch_id] = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        if (empty($initialBatchInfoFromCourses)) {
            return [];
        }

        $batchIdsToQueryEnrollmentEvent = array_keys($uniqueBatchIdsFromCourses);
        $batchIdToEndDateMap = [];

        if (!empty($batchIdsToQueryEnrollmentEvent)) {
            $queryEnrollmentEventBody = [
                'query' => [
                    'terms' => [
                        'data.details.batch_id' => $batchIdsToQueryEnrollmentEvent
                    ]
                ],
                '_source' => ['data.details.batch_id', 'data.details.batch_end_date'],
                'size' => count($batchIdsToQueryEnrollmentEvent) * 5 // Assuming up to 5 events per batch on average, adjust if needed or use a high fixed number
            ];

            $curlEnrollments = curl_init();
            curl_setopt_array($curlEnrollments, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . "batchenrollmentevent/_search",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST", // Using POST for query body
                CURLOPT_POSTFIELDS => json_encode($queryEnrollmentEventBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $responseEnrollments = curl_exec($curlEnrollments);
            $errEnrollments = curl_error($curlEnrollments);
            curl_close($curlEnrollments);

            if ($errEnrollments) {
                error_log('Elasticsearch search error for batchenrollmentevent in _getBatchesForInstructorFromCourses: ' . $errEnrollments);
                // Proceed without event dates if this fails, will use fallback from course
            } else if ($responseEnrollments) {
                $esResponseEnrollments = json_decode($responseEnrollments, true);
                if (!empty($esResponseEnrollments['hits']['hits'])) {
                    foreach ($esResponseEnrollments['hits']['hits'] as $hit) {
                        $event_batch_id = $hit['_source']['data']['details']['batch_id'] ?? null;
                        $event_batch_end_date = $hit['_source']['data']['details']['batch_end_date'] ?? null;
                        if ($event_batch_id && !isset($batchIdToEndDateMap[$event_batch_id])) { // Take first one found
                            $batchIdToEndDateMap[$event_batch_id] = $event_batch_end_date;
                        }
                    }
                }
            }
        }

        $finalLinkedBatchInfo = [];
        foreach ($initialBatchInfoFromCourses as $info) {
            $finalLinkedBatchInfo[] = [
                "batch_id" => $info["batch_id"],
                "instructor_id" => $info["instructor_id"],
                "batch_end_date" => $batchIdToEndDateMap[$info["batch_id"]] ?? $info["batch_end_date_from_course"] ?? null
            ];
        }

        return $finalLinkedBatchInfo;
    }

    // The helper methods _getCategoryFilterData and _getOrganizationFilterData
    // were removed in a previous step as they are now in InstructorFilter.php

    /**
     * Retrieves the mapped courses of an instructor.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or instructor ID to retrieve mapped courses.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false The formatted mapped courses data or false on failure.
     */
    public function getInstructorMappedCourses($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (!isset($query['id'])) {
            return false;
        }

        // Fetch instructor data from Elasticsearch
        $qryStr['_source'] = implode(',', [
            'data.details.user_id',
            'data.details.mapped_courses'
        ]);

        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id'], $qryStr);
        
        if ($instructorDataResponse['status_code'] != 200) {
            return false;
        }

        $instructor = $instructorDataResponse['body']['_source']['data']['details'];
        $mappedCourseIds = $instructor['mapped_courses'] ?? [];

        if (empty($mappedCourseIds) || !is_array($mappedCourseIds)) {
            $responseData = [
                'count' => 0,
                'data' => []
            ];
            
            // Apply schema validation for empty response
            return $this->schema->validate($responseData, ['count' => 'integer', 'data' => ['Refer#Course']], $filter);
        }

        $courseData = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId)) {
                try {
                    // Call getCourse with Course_Minimal schema specifically
                    $course = $this->courseModel->getCourse(['id' => $courseId], ['schema' => 'Course_Minimal']);
                    
                    if ($course && is_array($course) && isset($course['id'], $course['title'], $course['url'])) {
                        $courseData[] = $course;
                    }
                } catch (\Exception $e) {
                    error_log("Failed to load course {$courseId} for instructor {$query['id']}: " . $e->getMessage());
                }
            }
        }
        
        $responseData = [
            'count' => count($courseData),
            'data' => $courseData
        ];

        return $responseData;
    }

}
